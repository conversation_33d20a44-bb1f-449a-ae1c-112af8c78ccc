<?php
require_once __DIR__ . '/../config/database.php';

/**
 * Customer Panel Functions
 * Handles customer-specific operations including dashboard, bookings, profile, and rewards
 */

/**
 * Get comprehensive customer dashboard data
 */
function getCustomerDashboardData($customerId) {
    return [
        'profile' => getCustomerProfile($customerId),
        'upcomingBookings' => getUpcomingBookings($customerId, 3),
        'recentBookings' => getRecentBookings($customerId, 5),
        'pointsData' => getCustomerPointsData($customerId),
        'loyaltyTier' => getCustomerLoyaltyTierEnhanced($customerId),
        'stats' => getIndividualCustomerStats($customerId)
    ];
}

/**
 * Get customer profile information
 */
function getCustomerProfile($customerId) {
    global $database;
    
    $profile = $database->fetch("
        SELECT
            id, name, email, phone, date_of_birth, points,
            referral_code, referred_by, image as avatar, created_at
        FROM users
        WHERE id = ? AND role = 'CUSTOMER'
    ", [$customerId]);
    
    if (!$profile) {
        throw new Exception("Customer not found");
    }
    
    return $profile;
}

/**
 * Get upcoming bookings for customer
 */
function getUpcomingBookings($customerId, $limit = 5) {
    global $database;
    
    $bookings = $database->fetchAll("
        SELECT
            b.*,
            s.name as service_name,
            s.duration as service_duration,
            s.price as service_price,
            st.name as staff_name,
            NULL as staff_specialties
        FROM bookings b
        LEFT JOIN services s ON b.service_id = s.id
        LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
        WHERE b.user_id = ?
        AND b.date >= CURDATE()
        AND b.status NOT IN ('CANCELLED', 'NO_SHOW')
        ORDER BY b.date ASC, b.start_time ASC
        LIMIT ?
    ", [$customerId, $limit]);
    
    return $bookings;
}

/**
 * Get recent bookings for customer
 */
function getRecentBookings($customerId, $limit = 10) {
    global $database;
    
    $bookings = $database->fetchAll("
        SELECT
            b.*,
            s.name as service_name,
            s.duration as service_duration,
            s.price as service_price,
            st.name as staff_name
        FROM bookings b
        LEFT JOIN services s ON b.service_id = s.id
        LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
        WHERE b.user_id = ?
        ORDER BY b.created_at DESC
        LIMIT ?
    ", [$customerId, $limit]);
    
    return $bookings;
}

/**
 * Get customer points data and transaction history
 */
function getCustomerPointsData($customerId) {
    global $database;
    
    // Get current points
    $customer = $database->fetch("
        SELECT points FROM users WHERE id = ?
    ", [$customerId]);
    
    // Get recent point transactions
    $transactions = $database->fetchAll("
        SELECT * FROM point_transactions 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ", [$customerId]);
    
    // Calculate points earned this month
    $monthlyEarned = $database->fetch("
        SELECT COALESCE(SUM(points), 0) as earned
        FROM point_transactions 
        WHERE user_id = ? 
        AND type = 'EARNED'
        AND created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
    ", [$customerId]);
    
    // Calculate points redeemed this month
    $monthlyRedeemed = $database->fetch("
        SELECT COALESCE(SUM(ABS(points)), 0) as redeemed
        FROM point_transactions 
        WHERE user_id = ? 
        AND type = 'REDEMPTION'
        AND created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
    ", [$customerId]);
    
    return [
        'currentPoints' => intval($customer['points']),
        'monthlyEarned' => intval($monthlyEarned['earned']),
        'monthlyRedeemed' => intval($monthlyRedeemed['redeemed']),
        'transactions' => $transactions
    ];
}

/**
 * Get customer loyalty tier based on total spent (enhanced version using new database system)
 */
function getCustomerLoyaltyTierEnhanced($customerId) {
    global $database;

    // Include rewards functions for tier system
    require_once __DIR__ . '/rewards_functions.php';

    // Calculate total spent
    $totalSpent = $database->fetch("
        SELECT COALESCE(SUM(total_amount), 0) as total_spent
        FROM bookings
        WHERE user_id = ? AND status = 'COMPLETED'
    ", [$customerId]);

    $spent = intval($totalSpent['total_spent']); // Use integer for TSH

    // Get all tiers from database
    $allTiers = getCustomerTiers();

    // Find current tier
    $currentTier = getCustomerTier($spent);

    // Find next tier
    $nextTier = null;
    $nextTierAmount = 0;

    foreach ($allTiers as $tier) {
        if ($tier['minSpent'] > $spent) {
            $nextTier = $tier;
            $nextTierAmount = $tier['minSpent'] - $spent;
            break;
        }
    }

    // Extract color from Tailwind class (e.g., 'text-purple-600' -> 'purple')
    $colorClass = $currentTier['color'];
    $color = 'gray'; // default
    if (preg_match('/text-(\w+)-/', $colorClass, $matches)) {
        $color = $matches[1];
    }

    return [
        'name' => $currentTier['name'],
        'color' => $color,
        'minSpent' => $currentTier['minSpent'],
        'nextTier' => $nextTier ? $nextTier['name'] : null,
        'nextTierAmount' => $nextTierAmount,
        'benefits' => $currentTier['benefits'],
        'pointsMultiplier' => $currentTier['pointsMultiplier']
    ];
}

/**
 * Get individual customer statistics
 */
function getIndividualCustomerStats($customerId) {
    global $database;
    
    $stats = $database->fetch("
        SELECT 
            COUNT(*) as total_bookings,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_bookings,
            COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled_bookings,
            COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE 0 END), 0) as total_spent,
            COALESCE(AVG(CASE WHEN status = 'COMPLETED' THEN total_amount END), 0) as avg_booking_value,
            MIN(CASE WHEN status = 'COMPLETED' THEN date END) as first_visit,
            MAX(CASE WHEN status = 'COMPLETED' THEN date END) as last_visit
        FROM bookings 
        WHERE user_id = ?
    ", [$customerId]);
    
    return [
        'totalBookings' => intval($stats['total_bookings']),
        'completedBookings' => intval($stats['completed_bookings']),
        'cancelledBookings' => intval($stats['cancelled_bookings']),
        'totalSpent' => floatval($stats['total_spent']),
        'avgBookingValue' => floatval($stats['avg_booking_value']),
        'firstVisit' => $stats['first_visit'],
        'lastVisit' => $stats['last_visit']
    ];
}

/**
 * Update customer profile
 */
function updateCustomerProfile($customerId, $data) {
    global $database;
    
    $updateFields = [];
    $params = [];
    
    $allowedFields = ['name', 'phone', 'date_of_birth', 'image'];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateFields[] = "$field = ?";
            $params[] = $data[$field];
        }
    }
    
    if (empty($updateFields)) {
        throw new Exception("No fields to update");
    }
    
    $updateFields[] = "updated_at = NOW()";
    $params[] = $customerId;
    
    $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ? AND role = 'CUSTOMER'";
    
    $database->execute($sql, $params);
    
    return getCustomerProfile($customerId);
}

/**
 * Change customer password
 */
function changeCustomerPassword($customerId, $currentPassword, $newPassword) {
    global $database;
    
    // Verify current password
    $customer = $database->fetch("
        SELECT password FROM users WHERE id = ? AND role = 'CUSTOMER'
    ", [$customerId]);
    
    if (!$customer || !password_verify($currentPassword, $customer['password'])) {
        throw new Exception("Current password is incorrect");
    }
    
    // Update password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    $database->execute("
        UPDATE users 
        SET password = ?, updated_at = NOW() 
        WHERE id = ? AND role = 'CUSTOMER'
    ", [$hashedPassword, $customerId]);
    
    return true;
}

/**
 * Cancel booking
 */
function cancelCustomerBooking($customerId, $bookingId) {
    global $database;
    
    // Verify booking belongs to customer and can be cancelled
    $booking = $database->fetch("
        SELECT id, status, date, start_time 
        FROM bookings 
        WHERE id = ? AND user_id = ?
    ", [$bookingId, $customerId]);
    
    if (!$booking) {
        throw new Exception("Booking not found");
    }
    
    if ($booking['status'] === 'CANCELLED') {
        throw new Exception("Booking is already cancelled");
    }
    
    if ($booking['status'] === 'COMPLETED') {
        throw new Exception("Cannot cancel completed booking");
    }
    
    // Check if booking is within cancellation window (24 hours)
    $bookingDateTime = new DateTime($booking['date'] . ' ' . $booking['start_time']);
    $now = new DateTime();
    $hoursDiff = ($bookingDateTime->getTimestamp() - $now->getTimestamp()) / 3600;
    
    if ($hoursDiff < 24) {
        throw new Exception("Cannot cancel booking less than 24 hours in advance");
    }
    
    // Cancel booking
    $database->execute("
        UPDATE bookings
        SET status = 'CANCELLED', updated_at = NOW()
        WHERE id = ?
    ", [$bookingId]);

    // Create notification for booking cancellation
    createBookingNotification($bookingId, 'BOOKING_CANCELLED');

    return true;
}

/**
 * Get available time slots for booking (wrapper for booking_functions.php)
 */
function getCustomerAvailableTimeSlots($date, $serviceId, $staffId = null) {
    // Get service duration
    global $database;
    $service = $database->fetch("SELECT duration FROM services WHERE id = ?", [$serviceId]);

    if (!$service) {
        throw new Exception("Service not found");
    }

    $duration = intval($service['duration']);

    // Use the function from booking_functions.php
    return getAvailableTimeSlots($staffId, $date, $duration);
}

/**
 * Get available time slots for package booking
 */
function getCustomerAvailableTimeSlotsForPackage($date, $packageId, $staffId = null) {
    // Get package total duration
    global $database;

    // Get all services in the package and calculate total duration
    $packageServices = $database->fetchAll("
        SELECT s.duration
        FROM services s
        INNER JOIN package_services ps ON s.id = ps.service_id
        WHERE ps.package_id = ?
    ", [$packageId]);

    if (empty($packageServices)) {
        throw new Exception("Package not found or has no services");
    }

    // Calculate total duration for all services in package
    $totalDuration = array_sum(array_column($packageServices, 'duration'));

    // Use the function from booking_functions.php
    return getAvailableTimeSlots($staffId, $date, $totalDuration);
}

/**
 * Create new booking (supports single service, multi-service, and package bookings)
 */
function createCustomerBooking($customerId, $data) {
    global $database;

    // Validate required fields
    $required = ['staff_id', 'date', 'start_time', 'end_time'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            throw new Exception("Field '$field' is required");
        }
    }

    // Determine booking type and validate accordingly
    $bookingType = 'SINGLE_SERVICE';
    if (!empty($data['package_id'])) {
        $bookingType = 'PACKAGE';
    } elseif (!empty($data['service_ids']) && is_array($data['service_ids']) && count($data['service_ids']) > 1) {
        $bookingType = 'MULTI_SERVICE';
    } elseif (!empty($data['service_id']) || (!empty($data['service_ids']) && count($data['service_ids']) == 1)) {
        $bookingType = 'SINGLE_SERVICE';
    } else {
        throw new Exception("Either service_id, service_ids array, or package_id is required");
    }

    // Get service or package details and calculate total price
    $price = 0;
    $selectedServices = [];

    if ($bookingType === 'PACKAGE') {
        $package = $database->fetch("
            SELECT price FROM packages WHERE id = ?
        ", [$data['package_id']]);

        if (!$package) {
            throw new Exception("Package not found");
        }
        $price = $package['price'];

    } elseif ($bookingType === 'MULTI_SERVICE') {
        // Handle multiple services
        $serviceIds = $data['service_ids'];
        $placeholders = str_repeat('?,', count($serviceIds) - 1) . '?';
        $services = $database->fetchAll("
            SELECT id, name, price, duration FROM services WHERE id IN ($placeholders) AND is_active = 1
        ", $serviceIds);

        if (count($services) !== count($serviceIds)) {
            throw new Exception("One or more selected services not found or inactive");
        }

        foreach ($services as $service) {
            $price += $service['price'];
            $selectedServices[] = $service;
        }

    } else {
        // Single service
        $serviceId = !empty($data['service_id']) ? $data['service_id'] : $data['service_ids'][0];
        $service = $database->fetch("
            SELECT id, name, price, duration FROM services WHERE id = ? AND is_active = 1
        ", [$serviceId]);

        if (!$service) {
            throw new Exception("Service not found or inactive");
        }
        $price = $service['price'];
        $selectedServices[] = $service;
    }
    
    // Calculate total amount (considering points used)
    $pointsUsed = intval($data['points_used'] ?? 0);
    $pointsDiscount = $pointsUsed * 10; // 1 point = TSH 10
    $totalAmount = max(0, $price - $pointsDiscount);

    // Points will be earned only when booking is completed, not during creation
    $pointsEarned = 0;
    
    try {
        $database->beginTransaction();
        
        // Create booking
        $bookingId = generateUUID();

        // Determine service_id for single service bookings
        $singleServiceId = null;
        if ($bookingType === 'SINGLE_SERVICE') {
            if (!empty($data['service_id'])) {
                $singleServiceId = $data['service_id'];
            } elseif (!empty($data['service_ids']) && is_array($data['service_ids']) && count($data['service_ids']) == 1) {
                $singleServiceId = $data['service_ids'][0];
            }
        }

        $database->execute("
            INSERT INTO bookings (
                id, user_id, service_id, package_id, staff_id, date, start_time, end_time,
                total_amount, points_used, points_earned, notes, booking_type, status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'PENDING', NOW(), NOW())
        ", [
            $bookingId,
            $customerId,
            $singleServiceId,
            $data['package_id'] ?? null,
            $data['staff_id'],
            $data['date'],
            $data['start_time'],
            $data['end_time'],
            $totalAmount,
            $pointsUsed,
            $pointsEarned,
            $data['notes'] ?? null,
            $bookingType
        ]);

        // For multi-service bookings, insert into booking_services table
        if ($bookingType === 'MULTI_SERVICE') {
            foreach ($selectedServices as $service) {
                $database->execute("
                    INSERT INTO booking_services (id, booking_id, service_id, service_price, service_duration, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())
                ", [
                    generateUUID(),
                    $bookingId,
                    $service['id'],
                    $service['price'],
                    $service['duration']
                ]);
            }
        }
        
        // Update customer points (only deduct used points, don't award earned points yet)
        if ($pointsUsed > 0) {
            // Validate user has enough points
            $userPoints = $database->fetch("SELECT points FROM users WHERE id = ?", [$customerId])['points'];
            if ($userPoints < $pointsUsed) {
                throw new Exception("Insufficient points. You have {$userPoints} points but tried to use {$pointsUsed}.");
            }

            // Deduct used points
            $database->execute("
                UPDATE users
                SET points = points - ?, updated_at = NOW()
                WHERE id = ?
            ", [$pointsUsed, $customerId]);

            // Record point redemption transaction
            $database->execute("
                INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
                VALUES (?, ?, ?, ?, 'REDEMPTION', 'Points used for booking discount', NOW())
            ", [generateUUID(), $customerId, $bookingId, -$pointsUsed]);
        }
        
        $database->commit();

        // Create notification for new booking
        createBookingNotification($bookingId, 'BOOKING_NEW');

        // Create notification for assigned staff member
        require_once __DIR__ . '/notification_triggers.php';
        createStaffBookingNotification($bookingId, 'BOOKING_NEW');

        return $bookingId;
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}

/**
 * Get services for a multi-service booking
 */
function getBookingServices($bookingId) {
    global $database;

    return $database->fetchAll("
        SELECT
            bs.service_id,
            bs.service_price,
            bs.service_duration,
            s.name as service_name,
            s.category as service_category,
            s.description as service_description
        FROM booking_services bs
        JOIN services s ON bs.service_id = s.id
        WHERE bs.booking_id = ?
        ORDER BY s.name
    ", [$bookingId]);
}

/**
 * Get complete booking details including services
 */
function getBookingDetails($bookingId) {
    global $database;

    $booking = $database->fetch("
        SELECT
            b.*,
            u.name as customer_name,
            u.email as customer_email,
            u.phone as customer_phone,
            st.name as staff_name,
            st.email as staff_email,
            st.phone as staff_phone,
            s.name as service_name,
            s.price as service_price,
            s.duration as service_duration,
            s.category as service_category,
            p.name as package_name,
            p.price as package_price
        FROM bookings b
        LEFT JOIN users u ON b.user_id = u.id
        LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
        LEFT JOIN services s ON b.service_id = s.id
        LEFT JOIN packages p ON b.package_id = p.id
        WHERE b.id = ?
    ", [$bookingId]);

    if (!$booking) {
        return null;
    }

    // If it's a multi-service booking, get all services
    if ($booking['booking_type'] === 'MULTI_SERVICE') {
        $booking['services'] = getBookingServices($bookingId);
    } elseif ($booking['booking_type'] === 'PACKAGE') {
        // Get package services
        $booking['package_services'] = $database->fetchAll("
            SELECT
                s.id as service_id,
                s.name as service_name,
                s.price as service_price,
                s.duration as service_duration,
                s.category as service_category
            FROM package_services ps
            JOIN services s ON ps.service_id = s.id
            WHERE ps.package_id = ?
            ORDER BY s.name
        ", [$booking['package_id']]);
    }

    return $booking;
}
