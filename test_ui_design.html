<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Service UI Design Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for multi-service functionality */
        .service-cart-item {
            animation: slideInRight 0.3s ease-out;
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        /* Enhanced toggle switch styles */
        .toggle-switch {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .toggle-switch.active {
            background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
            border-color: #d4af37;
            box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
        }
        
        .toggle-dot {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .toggle-dot.active {
            transform: translateX(1.5rem);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .toggle-dot.active i {
            opacity: 1 !important;
            color: #d4af37 !important;
        }
        
        /* Enhanced checkbox styles */
        .service-select:checked + div {
            background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
            border-color: #d4af37;
            box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.3);
        }
        
        .service-select:checked + div i {
            opacity: 1 !important;
            transform: scale(1) !important;
            color: #000 !important;
        }
        
        /* Service card hover effects */
        .service-option:hover .service-checkbox {
            transform: scale(1.05);
        }
        
        .service-option.selected-multi {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.15) 0%, rgba(212, 175, 55, 0.08) 100%);
            border-color: #d4af37;
            box-shadow: 0 0 0 1px rgba(212, 175, 55, 0.3), 0 4px 12px rgba(212, 175, 55, 0.1);
            transform: translateY(-2px);
        }
        
        .cart-total-animation {
            animation: pulse 0.5s ease-in-out;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        /* Custom color classes */
        .bg-secondary-800 { background-color: #1f2937; }
        .bg-secondary-900 { background-color: #111827; }
        .bg-secondary-700 { background-color: #374151; }
        .bg-secondary-600 { background-color: #4b5563; }
        .bg-secondary-500 { background-color: #6b7280; }
        .border-secondary-700 { border-color: #374151; }
        .border-secondary-600 { border-color: #4b5563; }
        .border-secondary-500 { border-color: #6b7280; }
        .text-salon-gold { color: #d4af37; }
        .bg-salon-gold { background-color: #d4af37; }
        .border-salon-gold { border-color: #d4af37; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8 text-salon-gold">Multi-Service UI Design Test</h1>
        
        <!-- Multi-Service Selection Toggle -->
        <div class="mb-6 bg-gradient-to-r from-secondary-800/80 to-secondary-900/80 backdrop-blur-sm rounded-xl p-5 border border-secondary-700/50 hover:border-salon-gold/30 transition-all duration-300">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <label class="flex items-center cursor-pointer group">
                        <input type="checkbox" id="multiServiceToggle" class="sr-only">
                        <div class="relative">
                            <div class="w-12 h-6 bg-secondary-600 rounded-full shadow-inner transition-all duration-300 group-hover:bg-secondary-500 border border-secondary-500"></div>
                            <div class="absolute w-5 h-5 bg-white rounded-full shadow-lg top-0.5 left-0.5 transition-all duration-300 flex items-center justify-center">
                                <i class="fas fa-check text-xs text-secondary-600 opacity-0 transition-opacity duration-300"></i>
                            </div>
                        </div>
                    </label>
                    <div>
                        <span class="text-white font-semibold text-lg">Multi-Service Selection</span>
                        <p class="text-gray-400 text-sm mt-1">Select multiple services for one appointment</p>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-2 text-salon-gold">
                    <i class="fas fa-layer-group text-xl"></i>
                    <span class="text-sm font-medium">Bundle Services</span>
                </div>
            </div>
        </div>

        <!-- Service Cart -->
        <div id="serviceCart" class="hidden mb-8 bg-secondary-900/80 backdrop-blur-sm border border-salon-gold/30 rounded-xl p-6">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-shopping-cart mr-3 text-salon-gold"></i>
                    Selected Services
                </h4>
                <button type="button" id="clearCart" class="text-sm text-red-400 hover:text-red-300 transition-colors">
                    <i class="fas fa-trash mr-1"></i>Clear All
                </button>
            </div>
            <div id="cartItems" class="space-y-3 mb-4">
                <p class="text-gray-400 text-center py-4">No services selected</p>
            </div>
            <div class="border-t border-secondary-700 pt-4">
                <div class="flex justify-between items-center text-lg font-semibold">
                    <span class="text-white">Total Duration:</span>
                    <span class="text-salon-gold" id="cartTotalDuration">0 minutes</span>
                </div>
                <div class="flex justify-between items-center text-xl font-bold mt-2">
                    <span class="text-white">Total Price:</span>
                    <span class="text-salon-gold" id="cartTotalPrice">TSH 0.00</span>
                </div>
            </div>
        </div>

        <!-- Sample Services -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="service-option border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300 relative"
                 data-service-id="1" data-service-name="Hair Cut" data-service-price="25000" data-service-duration="30" data-service-category="Hair">
                
                <!-- Checkbox for multi-selection -->
                <div class="service-checkbox absolute top-3 right-3 hidden">
                    <label class="relative cursor-pointer group">
                        <input type="checkbox" class="service-select sr-only" value="1">
                        <div class="w-6 h-6 bg-secondary-700 border-2 border-secondary-500 rounded-lg transition-all duration-300 group-hover:border-salon-gold flex items-center justify-center">
                            <i class="fas fa-check text-salon-gold text-sm opacity-0 transition-all duration-300 transform scale-0"></i>
                        </div>
                    </label>
                </div>
                
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-semibold text-white text-lg">Hair Cut</h4>
                    <span class="text-salon-gold font-bold text-lg">TSH 25,000</span>
                </div>
                <p class="text-sm text-gray-400 mb-4 leading-relaxed">Professional hair cutting service with styling</p>
                <div class="flex items-center justify-between text-sm text-gray-500">
                    <span class="flex items-center"><i class="fas fa-clock mr-2 text-salon-gold"></i>30 minutes</span>
                    <span class="flex items-center"><i class="fas fa-tag mr-2 text-salon-gold"></i>Hair</span>
                </div>
            </div>

            <div class="service-option border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300 relative"
                 data-service-id="2" data-service-name="Facial Treatment" data-service-price="45000" data-service-duration="60" data-service-category="Facial">
                
                <!-- Checkbox for multi-selection -->
                <div class="service-checkbox absolute top-3 right-3 hidden">
                    <label class="relative cursor-pointer group">
                        <input type="checkbox" class="service-select sr-only" value="2">
                        <div class="w-6 h-6 bg-secondary-700 border-2 border-secondary-500 rounded-lg transition-all duration-300 group-hover:border-salon-gold flex items-center justify-center">
                            <i class="fas fa-check text-salon-gold text-sm opacity-0 transition-all duration-300 transform scale-0"></i>
                        </div>
                    </label>
                </div>
                
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-semibold text-white text-lg">Facial Treatment</h4>
                    <span class="text-salon-gold font-bold text-lg">TSH 45,000</span>
                </div>
                <p class="text-sm text-gray-400 mb-4 leading-relaxed">Deep cleansing facial with moisturizing treatment</p>
                <div class="flex items-center justify-between text-sm text-gray-500">
                    <span class="flex items-center"><i class="fas fa-clock mr-2 text-salon-gold"></i>60 minutes</span>
                    <span class="flex items-center"><i class="fas fa-tag mr-2 text-salon-gold"></i>Facial</span>
                </div>
            </div>

            <div class="service-option border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300 relative"
                 data-service-id="3" data-service-name="Manicure" data-service-price="20000" data-service-duration="45" data-service-category="Nails">
                
                <!-- Checkbox for multi-selection -->
                <div class="service-checkbox absolute top-3 right-3 hidden">
                    <label class="relative cursor-pointer group">
                        <input type="checkbox" class="service-select sr-only" value="3">
                        <div class="w-6 h-6 bg-secondary-700 border-2 border-secondary-500 rounded-lg transition-all duration-300 group-hover:border-salon-gold flex items-center justify-center">
                            <i class="fas fa-check text-salon-gold text-sm opacity-0 transition-all duration-300 transform scale-0"></i>
                        </div>
                    </label>
                </div>
                
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-semibold text-white text-lg">Manicure</h4>
                    <span class="text-salon-gold font-bold text-lg">TSH 20,000</span>
                </div>
                <p class="text-sm text-gray-400 mb-4 leading-relaxed">Complete nail care with polish application</p>
                <div class="flex items-center justify-between text-sm text-gray-500">
                    <span class="flex items-center"><i class="fas fa-clock mr-2 text-salon-gold"></i>45 minutes</span>
                    <span class="flex items-center"><i class="fas fa-tag mr-2 text-salon-gold"></i>Nails</span>
                </div>
            </div>

            <div class="service-option border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300 relative"
                 data-service-id="4" data-service-name="Massage" data-service-price="60000" data-service-duration="90" data-service-category="Wellness">
                
                <!-- Checkbox for multi-selection -->
                <div class="service-checkbox absolute top-3 right-3 hidden">
                    <label class="relative cursor-pointer group">
                        <input type="checkbox" class="service-select sr-only" value="4">
                        <div class="w-6 h-6 bg-secondary-700 border-2 border-secondary-500 rounded-lg transition-all duration-300 group-hover:border-salon-gold flex items-center justify-center">
                            <i class="fas fa-check text-salon-gold text-sm opacity-0 transition-all duration-300 transform scale-0"></i>
                        </div>
                    </label>
                </div>
                
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-semibold text-white text-lg">Relaxing Massage</h4>
                    <span class="text-salon-gold font-bold text-lg">TSH 60,000</span>
                </div>
                <p class="text-sm text-gray-400 mb-4 leading-relaxed">Full body relaxing massage with aromatherapy</p>
                <div class="flex items-center justify-between text-sm text-gray-500">
                    <span class="flex items-center"><i class="fas fa-clock mr-2 text-salon-gold"></i>90 minutes</span>
                    <span class="flex items-center"><i class="fas fa-tag mr-2 text-salon-gold"></i>Wellness</span>
                </div>
            </div>
        </div>

        <div class="mt-8 text-center">
            <p class="text-gray-400">Toggle the multi-service selection to test the UI functionality</p>
        </div>
    </div>

    <script>
        let isMultiServiceMode = false;
        let selectedServices = [];

        // Multi-service toggle functionality
        document.getElementById('multiServiceToggle').addEventListener('change', function() {
            isMultiServiceMode = this.checked;
            toggleMultiServiceMode();
        });

        function toggleMultiServiceMode() {
            const checkboxes = document.querySelectorAll('.service-checkbox');
            const cart = document.getElementById('serviceCart');
            
            if (isMultiServiceMode) {
                // Show checkboxes and cart
                checkboxes.forEach(cb => cb.classList.remove('hidden'));
                cart.classList.remove('hidden');
                
                // Update toggle appearance
                const toggle = document.getElementById('multiServiceToggle');
                const toggleBg = toggle.nextElementSibling.querySelector('div');
                const toggleDot = toggle.nextElementSibling.querySelector('div > div');
                const toggleIcon = toggleDot.querySelector('i');
                
                toggleBg.classList.add('toggle-switch', 'active');
                toggleBg.classList.remove('bg-secondary-600');
                toggleDot.classList.add('toggle-dot', 'active');
                if (toggleIcon) {
                    toggleIcon.style.opacity = '1';
                }
            } else {
                // Hide checkboxes and cart
                checkboxes.forEach(cb => cb.classList.add('hidden'));
                cart.classList.add('hidden');
                
                // Update toggle appearance
                const toggle = document.getElementById('multiServiceToggle');
                const toggleBg = toggle.nextElementSibling.querySelector('div');
                const toggleDot = toggle.nextElementSibling.querySelector('div > div');
                const toggleIcon = toggleDot.querySelector('i');
                
                toggleBg.classList.remove('toggle-switch', 'active');
                toggleBg.classList.add('bg-secondary-600');
                toggleDot.classList.remove('toggle-dot', 'active');
                if (toggleIcon) {
                    toggleIcon.style.opacity = '0';
                }
                
                // Clear selections
                selectedServices = [];
                document.querySelectorAll('.service-select').forEach(cb => cb.checked = false);
                document.querySelectorAll('.service-option').forEach(opt => {
                    opt.classList.remove('selected-multi');
                });
                updateServiceCart();
            }
        }

        // Service selection handlers
        document.querySelectorAll('.service-option').forEach(option => {
            option.addEventListener('click', function(e) {
                if (e.target.classList.contains('service-select')) {
                    return;
                }
                
                if (isMultiServiceMode) {
                    const checkbox = this.querySelector('.service-select');
                    checkbox.checked = !checkbox.checked;
                    handleServiceCheckboxChange(checkbox);
                }
            });
        });

        document.querySelectorAll('.service-select').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                handleServiceCheckboxChange(this);
            });
        });

        function handleServiceCheckboxChange(checkbox) {
            const serviceOption = checkbox.closest('.service-option');
            const serviceData = {
                id: serviceOption.dataset.serviceId,
                name: serviceOption.dataset.serviceName,
                price: parseFloat(serviceOption.dataset.servicePrice),
                duration: parseInt(serviceOption.dataset.serviceDuration),
                category: serviceOption.dataset.serviceCategory
            };

            if (checkbox.checked) {
                addToCart(serviceData);
                serviceOption.classList.add('selected-multi');
            } else {
                removeFromCart(serviceData.id);
                serviceOption.classList.remove('selected-multi');
            }
        }

        function addToCart(serviceData) {
            const existingIndex = selectedServices.findIndex(s => s.id === serviceData.id);
            if (existingIndex === -1) {
                selectedServices.push(serviceData);
                updateServiceCart();
            }
        }

        function removeFromCart(serviceId) {
            selectedServices = selectedServices.filter(s => s.id !== serviceId);
            updateServiceCart();
        }

        function updateServiceCart() {
            const cartItems = document.getElementById('cartItems');
            const totalDuration = document.getElementById('cartTotalDuration');
            const totalPrice = document.getElementById('cartTotalPrice');
            
            if (selectedServices.length === 0) {
                cartItems.innerHTML = '<p class="text-gray-400 text-center py-4">No services selected</p>';
                totalDuration.textContent = '0 minutes';
                totalPrice.textContent = 'TSH 0.00';
                return;
            }
            
            let cartHTML = '';
            let totalDur = 0;
            let totalPr = 0;
            
            selectedServices.forEach((service) => {
                totalDur += service.duration;
                totalPr += service.price;
                
                cartHTML += `
                    <div class="service-cart-item flex items-center justify-between bg-secondary-800/50 rounded-lg p-3 border border-secondary-700 hover:border-salon-gold/50 transition-all duration-300">
                        <div class="flex-1">
                            <h5 class="font-medium text-white">${service.name}</h5>
                            <p class="text-sm text-gray-400">${service.category} • ${service.duration} minutes</p>
                        </div>
                        <div class="flex items-center gap-3">
                            <span class="text-salon-gold font-semibold">TSH ${service.price.toLocaleString()}</span>
                            <button type="button" onclick="removeFromCart('${service.id}')" class="text-red-400 hover:text-red-300 transition-colors p-1 rounded hover:bg-red-400/10">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
            
            cartItems.innerHTML = cartHTML;
            totalDuration.textContent = totalDur + ' minutes';
            totalPrice.textContent = 'TSH ' + totalPr.toLocaleString();
            
            // Add animation to totals
            totalDuration.classList.add('cart-total-animation');
            totalPrice.classList.add('cart-total-animation');
            setTimeout(() => {
                totalDuration.classList.remove('cart-total-animation');
                totalPrice.classList.remove('cart-total-animation');
            }, 500);
        }

        // Clear cart button
        document.getElementById('clearCart').addEventListener('click', function() {
            selectedServices = [];
            document.querySelectorAll('.service-select').forEach(cb => cb.checked = false);
            document.querySelectorAll('.service-option').forEach(opt => {
                opt.classList.remove('selected-multi');
            });
            updateServiceCart();
        });
    </script>
</body>
</html>
