-- Multi-Service Booking Enhancement
-- This script adds support for multiple service selection in a single booking
-- Execute this script to enhance the booking system

USE flix_salonce2;

-- 1. Add booking_type column to existing bookings table
ALTER TABLE bookings 
ADD COLUMN IF NOT EXISTS booking_type ENUM('SINGLE_SERVICE', 'MULTI_SERVICE', 'PACKAGE') DEFAULT 'SINGLE_SERVICE';

-- 2. Create booking_services table for multi-service bookings
CREATE TABLE IF NOT EXISTS booking_services (
    id VARCHAR(36) PRIMARY KEY,
    booking_id VARCHAR(36) NOT NULL,
    service_id VARCHAR(36) NOT NULL,
    service_price DECIMAL(10,2) NOT NULL,
    service_duration INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE KEY unique_booking_service (booking_id, service_id)
);

-- 3. Update existing bookings to set correct booking_type
UPDATE bookings 
SET booking_type = CASE 
    WHEN package_id IS NOT NULL THEN 'PACKAGE'
    WHEN service_id IS NOT NULL THEN 'SINGLE_SERVICE'
    ELSE 'SINGLE_SERVICE'
END
WHERE booking_type = 'SINGLE_SERVICE';

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_booking_services_booking_id ON booking_services(booking_id);
CREATE INDEX IF NOT EXISTS idx_booking_services_service_id ON booking_services(service_id);
CREATE INDEX IF NOT EXISTS idx_bookings_type ON bookings(booking_type);

-- 5. Create a view for easy booking details retrieval
CREATE OR REPLACE VIEW booking_details AS
SELECT 
    b.id as booking_id,
    b.user_id,
    b.staff_id,
    b.date,
    b.start_time,
    b.end_time,
    b.status,
    b.total_amount,
    b.points_used,
    b.points_earned,
    b.notes,
    b.booking_type,
    b.created_at,
    b.updated_at,
    -- Single service details
    s.name as service_name,
    s.price as service_price,
    s.duration as service_duration,
    s.category as service_category,
    -- Package details
    p.name as package_name,
    p.price as package_price,
    -- Staff details
    st.name as staff_name,
    st.email as staff_email,
    st.phone as staff_phone,
    -- Customer details
    u.name as customer_name,
    u.email as customer_email,
    u.phone as customer_phone
FROM bookings b
LEFT JOIN services s ON b.service_id = s.id
LEFT JOIN packages p ON b.package_id = p.id
LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
LEFT JOIN users u ON b.user_id = u.id;

-- 6. Create a function to get all services for a multi-service booking
DELIMITER //
CREATE OR REPLACE FUNCTION GetBookingServices(booking_id VARCHAR(36))
RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE services_json JSON;
    
    SELECT JSON_ARRAYAGG(
        JSON_OBJECT(
            'service_id', bs.service_id,
            'service_name', s.name,
            'service_price', bs.service_price,
            'service_duration', bs.service_duration,
            'service_category', s.category
        )
    ) INTO services_json
    FROM booking_services bs
    JOIN services s ON bs.service_id = s.id
    WHERE bs.booking_id = booking_id;
    
    RETURN COALESCE(services_json, JSON_ARRAY());
END //
DELIMITER ;

-- 7. Create stored procedure for creating multi-service bookings
DELIMITER //
CREATE OR REPLACE PROCEDURE CreateMultiServiceBooking(
    IN p_booking_id VARCHAR(36),
    IN p_user_id VARCHAR(36),
    IN p_staff_id VARCHAR(36),
    IN p_date DATE,
    IN p_start_time TIME,
    IN p_end_time TIME,
    IN p_total_amount DECIMAL(10,2),
    IN p_points_used INT,
    IN p_points_earned INT,
    IN p_notes TEXT,
    IN p_services JSON
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE service_id VARCHAR(36);
    DECLARE service_price DECIMAL(10,2);
    DECLARE service_duration INT;
    DECLARE i INT DEFAULT 0;
    DECLARE service_count INT;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Create the main booking record
    INSERT INTO bookings (
        id, user_id, staff_id, date, start_time, end_time,
        total_amount, points_used, points_earned, notes, booking_type,
        status, created_at, updated_at
    ) VALUES (
        p_booking_id, p_user_id, p_staff_id, p_date, p_start_time, p_end_time,
        p_total_amount, p_points_used, p_points_earned, p_notes, 'MULTI_SERVICE',
        'PENDING', NOW(), NOW()
    );
    
    -- Get the number of services
    SET service_count = JSON_LENGTH(p_services);
    
    -- Insert each service into booking_services
    WHILE i < service_count DO
        SET service_id = JSON_UNQUOTE(JSON_EXTRACT(p_services, CONCAT('$[', i, '].service_id')));
        SET service_price = JSON_UNQUOTE(JSON_EXTRACT(p_services, CONCAT('$[', i, '].service_price')));
        SET service_duration = JSON_UNQUOTE(JSON_EXTRACT(p_services, CONCAT('$[', i, '].service_duration')));
        
        INSERT INTO booking_services (
            id, booking_id, service_id, service_price, service_duration, created_at
        ) VALUES (
            UUID(), p_booking_id, service_id, service_price, service_duration, NOW()
        );
        
        SET i = i + 1;
    END WHILE;
    
    COMMIT;
END //
DELIMITER ;

-- 8. Add comments for documentation
ALTER TABLE bookings COMMENT = 'Main bookings table supporting single service, multi-service, and package bookings';
ALTER TABLE booking_services COMMENT = 'Junction table for multi-service bookings, stores individual services within a booking';

-- 9. Verify the changes
SELECT 'Multi-service booking enhancement completed successfully' as status;
