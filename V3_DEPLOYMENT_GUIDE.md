# 🚀 V3 Black Theme Deployment Guide

## 📋 Quick Deployment Checklist

### ✅ Pre-Deployment
- [ ] Server meets requirements (PHP 8.0+, MySQL 8.0+)
- [ ] SSL certificate configured
- [ ] Email SMTP credentials ready
- [ ] Payment gateway credentials available
- [ ] Domain/subdomain configured

### ✅ Database Setup
- [ ] Create database `flix_salonce2`
- [ ] Import `database/flix_salonce2.sql`
- [ ] Verify all tables created successfully
- [ ] Test database connection

### ✅ Configuration
- [ ] Copy and configure `config/app.php`
- [ ] Copy and configure `config/database.php`
- [ ] Update APP_URL for production
- [ ] Configure email settings
- [ ] Set up payment gateway keys

### ✅ Security
- [ ] Update JWT_SECRET
- [ ] Set proper file permissions
- [ ] Remove setup files if present
- [ ] Configure .htaccess properly

### ✅ Testing
- [ ] Test admin login
- [ ] Test staff login  
- [ ] Test customer registration
- [ ] Test booking system
- [ ] Test payment processing
- [ ] Test email notifications

---

## 🗄️ Database Import Instructions

### Method 1: phpMyAdmin (Recommended)
1. Open phpMyAdmin
2. Create new database: `flix_salonce2`
3. Select the database
4. Go to Import tab
5. Choose file: `database/flix_salonce2.sql`
6. Click "Go" to import

### Method 2: Command Line
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE flix_salonce2;"

# Import complete database
mysql -u root -p flix_salonce2 < database/flix_salonce2.sql

# Verify import
mysql -u root -p flix_salonce2 -e "SHOW TABLES;"
```

---

## ⚙️ Configuration Files

### config/app.php
```php
<?php
// Application Settings
define('APP_NAME', 'Flix Salon & SPA');
define('APP_URL', 'https://yourdomain.com/flix-php'); // UPDATE THIS
define('APP_ENV', 'production'); // Change from 'development'

// Security
define('JWT_SECRET', 'your-unique-secret-key-here'); // CHANGE THIS!
define('BCRYPT_COST', 12);

// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>'); // UPDATE
define('SMTP_PASSWORD', 'your-app-password'); // UPDATE

// Payment Gateways
define('STRIPE_PUBLIC_KEY', 'pk_live_...'); // UPDATE
define('STRIPE_SECRET_KEY', 'sk_live_...'); // UPDATE
define('FLUTTERWAVE_PUBLIC_KEY', 'FLWPUBK_...'); // UPDATE
define('FLUTTERWAVE_SECRET_KEY', 'FLWSECK_...'); // UPDATE
?>
```

### config/database.php
```php
<?php
class Database {
    private $host = 'localhost';
    private $db_name = 'flix_salonce2';
    private $username = 'your_db_user'; // UPDATE
    private $password = 'your_db_password'; // UPDATE
    // ... rest of the class
}
?>
```

---

## 🔐 Security Checklist

### File Permissions
```bash
# Set proper permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod 755 uploads/
```

### .htaccess Security
Ensure your .htaccess includes:
```apache
# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Hide sensitive files
<Files "config/*.php">
    Require all denied
</Files>
```

---

## 📧 Email Configuration

### Gmail SMTP Setup
1. Enable 2-factor authentication
2. Generate app password
3. Use app password in SMTP_PASSWORD
4. Test email functionality

### Test Email
```php
// Test email sending
$test_email = '<EMAIL>';
$subject = 'V3 Email Test';
$message = 'Email system working correctly!';
// Use your email functions to send test
```

---

## 💳 Payment Gateway Setup

### Stripe
1. Create Stripe account
2. Get live API keys
3. Configure webhook endpoints
4. Test payment flow

### Flutterwave
1. Create Flutterwave account
2. Get live API keys
3. Configure webhook URLs
4. Test African payment methods

### DPO
1. Create DPO account
2. Get API credentials
3. Configure return URLs
4. Test payment processing

---

## 🧪 Testing Procedures

### Admin Panel Testing
```
1. Login: <EMAIL> / admin123
2. Test dashboard loading
3. Create new service
4. Create new staff member
5. View booking calendar
6. Test customer messaging
7. Check analytics
```

### Staff Panel Testing
```
1. Login: <EMAIL> / staff123
2. View assigned appointments
3. Update appointment status
4. Check notifications
5. Test schedule management
```

### Customer Testing
```
1. Register new account
2. Book appointment
3. Test payment processing
4. Check email confirmations
5. Test forgot password
6. View loyalty points
```

---

## 🚨 Troubleshooting

### Common Issues

**Database Connection Error**
- Check credentials in config/database.php
- Verify MySQL service running
- Test connection manually

**Email Not Sending**
- Verify SMTP credentials
- Check firewall settings
- Test with different email provider

**Payment Failures**
- Verify API keys are live keys
- Check webhook configurations
- Review payment gateway logs

**Permission Errors**
- Set correct file permissions
- Check uploads directory writable
- Verify .htaccess readable

---

## 📊 Post-Deployment Monitoring

### Daily Checks
- [ ] Monitor error logs
- [ ] Check email delivery
- [ ] Verify payment processing
- [ ] Review booking confirmations

### Weekly Maintenance
- [ ] Database backup
- [ ] Update security patches
- [ ] Review analytics
- [ ] Check system performance

---

## 🎯 Success Metrics

Your V3 deployment is successful when:
- ✅ All three panels load correctly
- ✅ Users can register and login
- ✅ Bookings can be created and managed
- ✅ Payments process successfully
- ✅ Emails send automatically
- ✅ Mobile interface works properly
- ✅ Admin can manage all aspects

---

## 🆘 Support

If you encounter issues:
1. Check this deployment guide
2. Review error logs
3. Consult V3_RELEASE_NOTES.md
4. Check GitHub issues
5. Create new issue if needed

**Repository**: https://github.com/Craqinho/flix-php  
**Branch**: v3-black-theme  
**Tag**: v3.0.0-black-theme

---

**🎉 Congratulations on deploying V3 Black Theme!**
