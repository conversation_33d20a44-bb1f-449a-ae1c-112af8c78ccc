<?php
/**
 * Get Services with Pagination API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    // Pagination settings
    $servicesPerPage = 10;
    $currentPage = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $offset = ($currentPage - 1) * $servicesPerPage;

    // Get total number of services
    $totalServices = $database->fetchAll("SELECT COUNT(*) as count FROM services WHERE is_active = 1")[0]['count'];
    $totalPages = ceil($totalServices / $servicesPerPage);

    // Get paginated services
    $services = $database->fetchAll(
        "SELECT * FROM services WHERE is_active = 1 ORDER BY name ASC LIMIT ? OFFSET ?", 
        [$servicesPerPage, $offset]
    );

    // Format services for JSON response
    $formattedServices = array_map(function($service) {
        return [
            'id' => $service['id'],
            'name' => htmlspecialchars($service['name']),
            'description' => htmlspecialchars($service['description']),
            'price' => floatval($service['price']),
            'duration' => intval($service['duration']),
            'category' => htmlspecialchars($service['category'])
        ];
    }, $services);

    echo json_encode([
        'success' => true,
        'services' => $formattedServices,
        'pagination' => [
            'currentPage' => $currentPage,
            'totalPages' => $totalPages,
            'totalServices' => $totalServices,
            'servicesPerPage' => $servicesPerPage
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error loading services: ' . $e->getMessage()
    ]);
}
?>
