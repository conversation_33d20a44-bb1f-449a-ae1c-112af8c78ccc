<?php
/**
 * Admin Bookings Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/booking_expiration.php';

// Run expiration check if needed
$expirationResult = runExpirationCheckIfNeeded();
if ($expirationResult && $expirationResult['expired_count'] > 0) {
    $_SESSION['success'] = "{$expirationResult['expired_count']} pending bookings have been marked as expired.";
}

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $bookingId = $_POST['booking_id'] ?? '';

    if ($action === 'update_status' && $bookingId) {
        $newStatus = $_POST['status'] ?? '';
        $result = updateBookingStatus($bookingId, $newStatus);

        if ($result['success']) {
            $_SESSION['success'] = 'Booking status updated successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }

        redirect('/admin/bookings');
    } elseif ($action === 'delete' && $bookingId) {
        $result = deleteBooking($bookingId);

        if ($result['success']) {
            $_SESSION['success'] = 'Booking deleted successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }

        redirect('/admin/bookings');
    }
}

// Get bookings with filters
$page = (int)($_GET['page'] ?? 1);
$limit = 15;
$offset = ($page - 1) * $limit;
$status = sanitize($_GET['status'] ?? '');
$date = sanitize($_GET['date'] ?? '');
$search = sanitize($_GET['search'] ?? '');

$whereClause = "WHERE 1=1";
$params = [];

if ($status) {
    $whereClause .= " AND b.status = ?";
    $params[] = $status;
}

if ($date) {
    $whereClause .= " AND DATE(b.date) = ?";
    $params[] = $date;
}

if ($search) {
    $whereClause .= " AND (u.name LIKE ? OR u.email LIKE ? OR s.name LIKE ? OR p.name LIKE ? OR st.name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$bookings = $database->fetchAll(
    "SELECT b.*, u.name as customer_name, u.email as customer_email, u.phone as customer_phone,
            s.name as service_name, s.price as service_price, s.duration as service_duration,
            p.name as package_name, p.price as package_price,
            st.name as staff_name, st.email as staff_email
     FROM bookings b
     LEFT JOIN users u ON b.user_id = u.id
     LEFT JOIN services s ON b.service_id = s.id
     LEFT JOIN packages p ON b.package_id = p.id
     LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
     $whereClause
     GROUP BY b.id
     ORDER BY b.date DESC, b.start_time DESC
     LIMIT $limit OFFSET $offset",
    $params
);

// Enhance bookings with multi-service information
foreach ($bookings as &$booking) {
    if ($booking['booking_type'] === 'MULTI_SERVICE') {
        $multiServices = $database->fetchAll("
            SELECT bs.*, s.name as service_name, s.category
            FROM booking_services bs
            JOIN services s ON bs.service_id = s.id
            WHERE bs.booking_id = ?
            ORDER BY s.name
        ", [$booking['id']]);

        if (!empty($multiServices)) {
            $serviceNames = array_column($multiServices, 'service_name');
            $booking['service_name'] = implode(', ', $serviceNames);
            $booking['service_duration'] = array_sum(array_column($multiServices, 'service_duration'));
            $booking['multi_services'] = $multiServices;
        }
    }
}

// Calculate duration for package bookings
foreach ($bookings as $index => $booking) {
    if (!empty($booking['package_id']) && empty($booking['service_duration'])) {
        // Get total duration for package
        $packageServices = $database->fetchAll("
            SELECT s.duration
            FROM services s
            INNER JOIN package_services ps ON s.id = ps.service_id
            WHERE ps.package_id = ?
        ", [$booking['package_id']]);

        $bookings[$index]['service_duration'] = array_sum(array_column($packageServices, 'duration'));
    }
}

$totalBookings = $database->fetch(
    "SELECT COUNT(DISTINCT b.id) as count FROM bookings b
     LEFT JOIN users u ON b.user_id = u.id
     LEFT JOIN services s ON b.service_id = s.id
     LEFT JOIN packages p ON b.package_id = p.id
     LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
     $whereClause",
    $params
)['count'];

$totalPages = ceil($totalBookings / $limit);

// Get booking statistics
$stats = getBookingStats();

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Bookings Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 shadow-xl rounded-2xl p-8 mb-8 hover-lift">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-white font-serif">Bookings <span class="text-salon-gold">Management</span></h1>
                            <p class="mt-2 text-lg text-gray-300">Manage customer appointments and schedules</p>
                        </div>
                        <div class="mt-6 sm:mt-0 flex gap-4">
                            <a href="<?= getBasePath() ?>/admin/bookings/calendar.php"
                               class="bg-secondary-800/50 border border-secondary-700 text-white px-6 py-3 rounded-xl font-semibold hover:bg-secondary-700 hover:border-salon-gold/50 transition-all duration-300 hover-lift">
                                Calendar View
                            </a>
                            <a href="<?= getBasePath() ?>/admin/bookings/create.php"
                               class="bg-salon-gold text-black px-6 py-3 rounded-xl font-semibold hover:bg-gold-light transition-all duration-300 hover:scale-105 shadow-lg">
                                New Booking
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Message Display -->
                <?php if ($message): ?>
                    <div class="mb-8 p-6 rounded-xl <?= $messageType === 'success' ? 'bg-salon-gold/10 border border-salon-gold text-salon-gold' : 'bg-red-500/10 border border-red-500 text-red-400' ?> backdrop-blur-sm">
                        <div class="flex items-center">
                            <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?> mr-3 text-xl"></i>
                            <?= htmlspecialchars($message) ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-6 hover-lift">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                    <svg class="h-6 w-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-400 truncate">Total Bookings</dt>
                                    <dd class="text-2xl font-bold text-white"><?= number_format($stats['total']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-6 hover-lift">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                    <svg class="h-6 w-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-400 truncate">Pending</dt>
                                    <dd class="text-2xl font-bold text-salon-gold"><?= number_format($stats['pending']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-6 hover-lift">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                    <svg class="h-6 w-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-400 truncate">Confirmed</dt>
                                    <dd class="text-2xl font-bold text-white"><?= number_format($stats['confirmed']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-6 hover-lift">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                    <svg class="h-6 w-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-400 truncate">Today's Revenue</dt>
                                    <dd class="text-2xl font-bold text-salon-gold"><?= formatCurrency($stats['today_revenue']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 shadow-xl rounded-2xl p-8 mb-8 hover-lift">
                    <form method="GET" class="flex flex-col sm:flex-row gap-6">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                                   placeholder="Search by customer, service, or staff..."
                                   class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                        </div>
                        <div>
                            <select name="status" class="px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                                <option value="">All Status</option>
                                <option value="PENDING" <?= $status === 'PENDING' ? 'selected' : '' ?>>Pending</option>
                                <option value="CONFIRMED" <?= $status === 'CONFIRMED' ? 'selected' : '' ?>>Confirmed</option>
                                <option value="COMPLETED" <?= $status === 'COMPLETED' ? 'selected' : '' ?>>Completed</option>
                                <option value="CANCELLED" <?= $status === 'CANCELLED' ? 'selected' : '' ?>>Cancelled</option>
                                <option value="NO_SHOW" <?= $status === 'NO_SHOW' ? 'selected' : '' ?>>No Show</option>
                                <option value="EXPIRED" <?= $status === 'EXPIRED' ? 'selected' : '' ?>>Expired</option>
                            </select>
                        </div>
                        <div>
                            <input type="date" name="date" value="<?= htmlspecialchars($date) ?>"
                                   class="px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                        </div>
                        <button type="submit" class="bg-salon-gold text-black px-6 py-3 rounded-xl font-semibold hover:bg-gold-light transition-all duration-300 hover:scale-105 shadow-lg">
                            Filter
                        </button>
                        <?php if ($search || $status || $date): ?>
                            <a href="<?= getBasePath() ?>/admin/bookings" class="bg-secondary-800/50 border border-secondary-700 text-white px-6 py-3 rounded-xl font-semibold hover:bg-secondary-700 hover:border-salon-gold/50 transition-all duration-300">
                                Clear
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Bookings Table -->
                <div class="bg-secondary-800 shadow rounded-lg overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-secondary-700">
                            <thead class="bg-secondary-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Service</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Staff</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date & Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-secondary-800 divide-y divide-secondary-700">
                                <?php foreach ($bookings as $booking): ?>
                                    <tr class="hover:bg-secondary-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-salon-gold flex items-center justify-center">
                                                        <span class="text-sm font-medium text-black">
                                                            <?= strtoupper(substr($booking['customer_name'], 0, 2)) ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-white"><?= htmlspecialchars($booking['customer_name']) ?></div>
                                                    <div class="text-sm text-gray-300"><?= htmlspecialchars($booking['customer_email']) ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-white">
                                                <?php if ($booking['booking_type'] === 'MULTI_SERVICE' && !empty($booking['multi_services'])): ?>
                                                    <?php
                                                    $serviceNames = array_column($booking['multi_services'], 'service_name');
                                                    if (count($serviceNames) > 2) {
                                                        echo htmlspecialchars(implode(', ', array_slice($serviceNames, 0, 2))) . ' <span class="text-salon-gold">+' . (count($serviceNames) - 2) . ' more</span>';
                                                    } else {
                                                        echo htmlspecialchars(implode(', ', $serviceNames));
                                                    }
                                                    ?>
                                                    <span class="text-xs bg-blue-600 text-white px-2 py-1 rounded ml-2">MULTI</span>
                                                <?php elseif (!empty($booking['service_name'])): ?>
                                                    <?= htmlspecialchars($booking['service_name']) ?>
                                                <?php elseif (!empty($booking['package_name'])): ?>
                                                    <?= htmlspecialchars($booking['package_name']) ?>
                                                    <span class="text-xs bg-salon-gold text-black px-2 py-1 rounded ml-2">PACKAGE</span>
                                                <?php else: ?>
                                                    Unknown Service
                                                <?php endif; ?>
                                            </div>
                                            <div class="text-sm text-gray-300"><?= $booking['service_duration'] ?: 0 ?> min</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-white"><?= htmlspecialchars($booking['staff_name'] ?? 'Unassigned') ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-white"><?= date('M j, Y', strtotime($booking['date'])) ?></div>
                                            <div class="text-sm text-gray-300"><?= date('g:i A', strtotime($booking['start_time'])) ?> - <?= date('g:i A', strtotime($booking['end_time'])) ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-salon-gold"><?= formatCurrency($booking['total_amount']) ?></div>
                                            <?php if ($booking['points_used'] > 0): ?>
                                                <div class="text-xs text-gray-400"><?= $booking['points_used'] ?> points used</div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php
                                            $statusInfo = getBookingStatusInfo($booking['status']);
                                            ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusInfo['class'] ?>">
                                                <i class="<?= $statusInfo['icon'] ?> mr-1"></i>
                                                <?= $statusInfo['label'] ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex gap-2">
                                                <button onclick="viewBooking('<?= $booking['id'] ?>')"
                                                        class="text-blue-400 hover:text-blue-300">
                                                    View
                                                </button>
                                                <button onclick="editBooking('<?= $booking['id'] ?>')"
                                                        class="text-green-400 hover:text-green-300">
                                                    Edit
                                                </button>
                                                <button onclick="updateStatus('<?= $booking['id'] ?>', '<?= $booking['status'] ?>')"
                                                        class="text-salon-gold hover:text-gold-light">
                                                    Status
                                                </button>
                                                <?php if (in_array($booking['status'], ['CANCELLED', 'NO_SHOW'])): ?>
                                                    <button onclick="deleteBooking('<?= $booking['id'] ?>')"
                                                            class="text-red-400 hover:text-red-300">
                                                        Delete
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="bg-secondary-700 px-4 py-3 flex items-center justify-between border-t border-secondary-600 sm:px-6">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <?php if ($page > 1): ?>
                                    <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>" 
                                       class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                        Previous
                                    </a>
                                <?php endif; ?>
                                <?php if ($page < $totalPages): ?>
                                    <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>" 
                                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                        Next
                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-300">
                                        Showing <span class="font-medium"><?= $offset + 1 ?></span> to <span class="font-medium"><?= min($offset + $limit, $totalBookings) ?></span> of <span class="font-medium"><?= $totalBookings ?></span> results
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                        <?php for ($i = 1; $i <= min($totalPages, 10); $i++): ?>
                                            <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>" 
                                               class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?= $i === $page ? 'z-10 bg-salon-gold border-salon-gold text-black' : 'bg-secondary-700 border-secondary-600 text-gray-300 hover:bg-secondary-600' ?>">
                                                <?= $i ?>
                                            </a>
                                        <?php endfor; ?>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Modern Notification System -->
<div id="notificationContainer" class="fixed top-4 right-4 z-[80] space-y-2"></div>

<!-- Edit Booking Modal -->
<div id="editBookingModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-white">Edit Booking</h2>
            <button onclick="closeEditModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div id="editBookingContent">
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-salon-gold mx-auto"></div>
                <p class="text-gray-300 mt-2">Loading booking details...</p>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div id="statusModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-white">Update Booking Status</h2>
            <button onclick="closeStatusModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="statusForm" method="POST">
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="booking_id" id="statusBookingId">
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">New Status</label>
                <select name="status" id="newStatus" required 
                        class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    <option value="PENDING">Pending</option>
                    <option value="CONFIRMED">Confirmed</option>
                    <option value="IN_PROGRESS">In Progress</option>
                    <option value="COMPLETED">Completed</option>
                    <option value="CANCELLED">Cancelled</option>
                    <option value="NO_SHOW">No Show</option>
                    <option value="EXPIRED">Expired</option>
                </select>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Update Status
                </button>
                <button type="button" onclick="closeStatusModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Modern Notification System
function showNotification(message, type = 'success', duration = 5000) {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');

    // Set notification styles based on type
    let bgColor, textColor, icon;
    switch (type) {
        case 'success':
            bgColor = 'bg-green-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>`;
            break;
        case 'error':
            bgColor = 'bg-red-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>`;
            break;
        case 'warning':
            bgColor = 'bg-yellow-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>`;
            break;
        case 'info':
            bgColor = 'bg-blue-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>`;
            break;
        default:
            bgColor = 'bg-gray-600';
            textColor = 'text-white';
            icon = '';
    }

    notification.className = `${bgColor} ${textColor} px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 min-w-80 max-w-md transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;
    notification.innerHTML = `
        <div class="flex-shrink-0">${icon}</div>
        <div class="flex-1">
            <p class="font-medium">${message}</p>
        </div>
        <button onclick="removeNotification(this.parentElement)" class="flex-shrink-0 ml-4 text-white hover:text-gray-200">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

    container.appendChild(notification);

    // Trigger animation
    setTimeout(() => {
        notification.classList.remove('translate-x-full', 'opacity-0');
        notification.classList.add('translate-x-0', 'opacity-100');
    }, 100);

    // Auto remove after duration
    if (duration > 0) {
        setTimeout(() => {
            removeNotification(notification);
        }, duration);
    }

    return notification;
}

function removeNotification(notification) {
    notification.classList.add('translate-x-full', 'opacity-0');
    setTimeout(() => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    }, 300);
}

function showConfirmDialog(message, onConfirm, onCancel = null) {
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[90]';

    overlay.innerHTML = `
        <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4 transform transition-all duration-300 scale-95 opacity-0">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0 w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-white">Confirm Action</h3>
                </div>
            </div>
            <div class="mb-6">
                <p class="text-gray-300">${message}</p>
            </div>
            <div class="flex gap-3">
                <button onclick="handleConfirm(true)" class="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-red-700 transition-colors">
                    Confirm
                </button>
                <button onclick="handleConfirm(false)" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(overlay);

    // Trigger animation
    setTimeout(() => {
        const dialog = overlay.querySelector('div');
        dialog.classList.remove('scale-95', 'opacity-0');
        dialog.classList.add('scale-100', 'opacity-100');
    }, 100);

    // Handle confirm/cancel
    window.handleConfirm = function(confirmed) {
        overlay.classList.add('opacity-0');
        setTimeout(() => {
            document.body.removeChild(overlay);
            delete window.handleConfirm;
        }, 300);

        if (confirmed && onConfirm) {
            onConfirm();
        } else if (!confirmed && onCancel) {
            onCancel();
        }
    };

    // Close on backdrop click
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            window.handleConfirm(false);
        }
    });
}

// Edit Booking Functions
function editBooking(bookingId) {
    console.log('🚀 Opening edit modal for booking:', bookingId);
    document.getElementById('editBookingModal').classList.remove('hidden');
    loadBookingDetails(bookingId);
}

function closeEditModal() {
    document.getElementById('editBookingModal').classList.add('hidden');
}

function loadBookingDetails(bookingId) {
    fetch(`<?= getBasePath() ?>/api/admin/booking-details.php?id=${bookingId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayEditForm(data.booking);
            } else {
                document.getElementById('editBookingContent').innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-red-400">Error loading booking: ${data.error}</p>
                        <button onclick="closeEditModal()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded-lg">Close</button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('editBookingContent').innerHTML = `
                <div class="text-center py-8">
                    <p class="text-red-400">Error loading booking details</p>
                    <button onclick="closeEditModal()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded-lg">Close</button>
                </div>
            `;
        });
}

function displayEditForm(booking) {
    const formHtml = `
        <form id="editBookingForm" onsubmit="updateBooking(event)">
            <input type="hidden" id="editBookingId" value="${booking.id}">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Customer Information -->
                <div class="md:col-span-2">
                    <h3 class="text-lg font-semibold text-white mb-4">Customer Information</h3>
                    <div class="bg-secondary-700 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="h-12 w-12 rounded-full bg-salon-gold flex items-center justify-center">
                                <span class="text-sm font-medium text-black">
                                    ${booking.customer_name.substring(0, 2).toUpperCase()}
                                </span>
                            </div>
                            <div class="ml-4">
                                <div class="text-white font-medium">${booking.customer_name}</div>
                                <div class="text-gray-300">${booking.customer_email}</div>
                                <div class="text-gray-300">${booking.customer_phone || 'No phone'}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service/Package Selection -->
                <div class="md:col-span-2">
                    <div class="mb-4">
                        <div class="flex border-b border-secondary-600">
                            <button type="button" id="serviceTab" onclick="switchToServices()"
                                    class="px-4 py-2 text-sm font-medium text-white border-b-2 border-salon-gold">
                                Individual Services
                            </button>
                            <button type="button" id="packageTab" onclick="switchToPackages()"
                                    class="px-4 py-2 text-sm font-medium text-gray-400 border-b-2 border-transparent hover:text-white">
                                Service Packages
                            </button>
                        </div>
                    </div>

                    <!-- Service Selection -->
                    <div id="serviceSelection">
                        <label class="block text-sm font-medium text-gray-300 mb-2">Service</label>
                        <select id="editServiceId"
                                class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                onchange="updateServiceDetails()">
                            <option value="">Select Service</option>
                        </select>
                    </div>

                    <!-- Package Selection -->
                    <div id="packageSelection" class="hidden">
                        <label class="block text-sm font-medium text-gray-300 mb-2">Package</label>
                        <select id="editPackageId"
                                class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                onchange="updatePackageDetails()">
                            <option value="">Select Package</option>
                        </select>
                    </div>
                </div>

                <!-- Staff Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Staff Member</label>
                    <select id="editStaffId" required
                            class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="">Select Staff</option>
                    </select>
                </div>

                <!-- Date -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Date</label>
                    <input type="date" id="editDate" value="${booking.date}" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold"
                           onchange="updateAvailableSlots()">
                </div>

                <!-- Time -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Start Time</label>
                    <input type="time" id="editStartTime" value="${booking.start_time.substring(0, 5)}" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>

                <!-- Status -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                    <select id="editStatus" required
                            class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="PENDING" ${booking.status === 'PENDING' ? 'selected' : ''}>Pending</option>
                        <option value="CONFIRMED" ${booking.status === 'CONFIRMED' ? 'selected' : ''}>Confirmed</option>
                        <option value="IN_PROGRESS" ${booking.status === 'IN_PROGRESS' ? 'selected' : ''}>In Progress</option>
                        <option value="COMPLETED" ${booking.status === 'COMPLETED' ? 'selected' : ''}>Completed</option>
                        <option value="CANCELLED" ${booking.status === 'CANCELLED' ? 'selected' : ''}>Cancelled</option>
                        <option value="NO_SHOW" ${booking.status === 'NO_SHOW' ? 'selected' : ''}>No Show</option>
                        <option value="EXPIRED" ${booking.status === 'EXPIRED' ? 'selected' : ''}>Expired</option>
                    </select>
                </div>

                <!-- Total Amount -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Total Amount</label>
                    <input type="number" id="editTotalAmount" value="${booking.total_amount}" step="1" required
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>

                <!-- Notes -->
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Notes</label>
                    <textarea id="editNotes" rows="3"
                              class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold"
                              placeholder="Additional notes...">${booking.notes || ''}</textarea>
                </div>
            </div>

            <div class="flex gap-4 mt-6">
                <button type="submit" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Update Booking
                </button>
                <button type="button" onclick="closeEditModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    `;

    document.getElementById('editBookingContent').innerHTML = formHtml;

    // Load all data and set up the form
    console.log('🔄 Loading booking data:', booking);
    console.log('📋 Booking type analysis:', {
        service_id: booking.service_id,
        package_id: booking.package_id,
        service_name: booking.service_name,
        package_name: booking.package_name
    });

    // Load services and staff first
    loadServicesForEdit(booking.service_id);
    loadStaffForEdit(booking.staff_id);

    // Determine if this is a package or service booking with robust detection
    const isPackageBooking = booking.package_id &&
                            booking.package_id !== null &&
                            booking.package_id !== '' &&
                            booking.package_id !== 'null';

    console.log('🎯 Booking type determined:', isPackageBooking ? 'PACKAGE' : 'SERVICE');

    if (isPackageBooking) {
        console.log('📦 Package booking detected, package_id:', booking.package_id);

        // Load packages first, then switch to packages tab and select the package
        loadPackagesForEdit(booking.package_id).then(() => {
            console.log('✅ Packages loaded, switching to package tab');
            switchToPackages();

            // Ensure package is selected after tab switch
            setTimeout(() => {
                const packageSelect = document.getElementById('editPackageId');
                if (packageSelect && booking.package_id) {
                    packageSelect.value = booking.package_id;
                    console.log('📦 Package pre-selected:', booking.package_id);
                    updatePackageDetails();
                }
            }, 100);
        }).catch(error => {
            console.error('❌ Failed to load packages for package booking:', error);
            // Fallback to services tab if package loading fails
            switchToServices();
        });
    } else {
        console.log('🔧 Service booking detected, service_id:', booking.service_id);

        // Load packages first (empty), then switch to services tab
        loadPackagesForEdit(null).then(() => {
            console.log('✅ Packages loaded, switching to service tab');
            switchToServices();

            // Ensure service is selected after tab switch
            setTimeout(() => {
                const serviceSelect = document.getElementById('editServiceId');
                if (serviceSelect && booking.service_id) {
                    serviceSelect.value = booking.service_id;
                    console.log('🔧 Service pre-selected:', booking.service_id);
                    updateServiceDetails();
                }
            }, 100);
        }).catch(error => {
            console.error('❌ Failed to load packages for service booking:', error);
            // Still switch to services tab even if package loading fails
            switchToServices();
        });
    }

    // Add event listeners for dynamic updates
    setTimeout(() => {
        // Update price when service changes
        const serviceSelect = document.getElementById('editServiceId');
        if (serviceSelect) {
            serviceSelect.addEventListener('change', function() {
                console.log('🔧 Service changed to:', this.value);
                updateServiceDetails();
            });
            console.log('✅ Service change listener attached');
        }

        // Update price when package changes
        const packageSelect = document.getElementById('editPackageId');
        if (packageSelect) {
            packageSelect.addEventListener('change', function() {
                console.log('📦 Package changed to:', this.value);
                updatePackageDetails();
            });
            console.log('✅ Package change listener attached');
        }
    }, 500);
}

function updateStatus(bookingId, currentStatus) {
    document.getElementById('statusBookingId').value = bookingId;
    document.getElementById('newStatus').value = currentStatus;
    document.getElementById('statusModal').classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
}

function viewBooking(bookingId) {
    window.location.href = `<?= getBasePath() ?>/admin/bookings/view.php?id=${bookingId}`;
}

function deleteBooking(bookingId) {
    showConfirmDialog(
        'Are you sure you want to delete this booking? This action cannot be undone.',
        function() {
            // User confirmed deletion
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="booking_id" value="${bookingId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    );
}

// Manual time change handler removed - using direct time input only

// Additional Edit Functions
function loadServicesForEdit(selectedServiceId) {
    fetch('<?= getBasePath() ?>/api/admin/services.php')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('editServiceId');
            select.innerHTML = '<option value="">Select Service</option>';

            // Handle both response formats
            const services = data.services || data.data || [];
            services.forEach(service => {
                const option = document.createElement('option');
                option.value = service.id;
                option.textContent = `${service.name} - ${service.duration}min - TSH ${service.price}`;
                if (service.id == selectedServiceId) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        });
}

function loadPackagesForEdit(selectedPackageId) {
    console.log('📦 Loading packages, selected ID:', selectedPackageId);

    return fetch('<?= getBasePath() ?>/api/admin/packages.php')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('editPackageId');
            if (!select) {
                console.warn('⚠️ Package select element not found');
                return;
            }

            select.innerHTML = '<option value="">Select Package</option>';

            const packages = data.packages || data.data || [];
            console.log('📦 Loaded packages:', packages.length);

            packages.forEach(package => {
                const option = document.createElement('option');
                option.value = package.id;
                option.textContent = `${package.name} - TSH ${package.price}`;
                select.appendChild(option);
            });

            // Set selected package if provided
            if (selectedPackageId) {
                select.value = selectedPackageId;
                console.log('📦 Set package to:', selectedPackageId, 'Current value:', select.value);

                // Update price after selection
                setTimeout(() => {
                    if (select.value === selectedPackageId) {
                        updatePackageDetails();
                        console.log('✅ Package pre-selected successfully');
                    }
                }, 100);
            }

            console.log('✅ Package loading completed');
            return true;
        })
        .catch(error => {
            console.error('❌ Error loading packages:', error);
            const select = document.getElementById('editPackageId');
            if (select) {
                select.innerHTML = '<option value="">Error loading packages</option>';
            }
            throw error; // Re-throw to handle in calling code
        });
}

function switchToServices() {
    console.log('🔧 Switching to services tab');

    // Update tab appearance
    const serviceTab = document.getElementById('serviceTab');
    const packageTab = document.getElementById('packageTab');

    if (serviceTab && packageTab) {
        serviceTab.className = 'px-4 py-2 text-sm font-medium text-white border-b-2 border-salon-gold';
        packageTab.className = 'px-4 py-2 text-sm font-medium text-gray-400 border-b-2 border-transparent hover:text-white';
    }

    // Show/hide sections
    const serviceSelection = document.getElementById('serviceSelection');
    const packageSelection = document.getElementById('packageSelection');

    if (serviceSelection && packageSelection) {
        serviceSelection.classList.remove('hidden');
        packageSelection.classList.add('hidden');
    }

    // Clear package selection and make service required
    const serviceSelect = document.getElementById('editServiceId');
    const packageSelect = document.getElementById('editPackageId');

    if (packageSelect) packageSelect.value = '';
    if (serviceSelect) serviceSelect.required = true;
    if (packageSelect) packageSelect.required = false;

    console.log('✅ Switched to services tab');
}

function switchToPackages() {
    console.log('📦 Switching to packages tab');

    // Update tab appearance
    const serviceTab = document.getElementById('serviceTab');
    const packageTab = document.getElementById('packageTab');

    if (serviceTab && packageTab) {
        packageTab.className = 'px-4 py-2 text-sm font-medium text-white border-b-2 border-salon-gold';
        serviceTab.className = 'px-4 py-2 text-sm font-medium text-gray-400 border-b-2 border-transparent hover:text-white';
    }

    // Show/hide sections
    const serviceSelection = document.getElementById('serviceSelection');
    const packageSelection = document.getElementById('packageSelection');

    if (serviceSelection && packageSelection) {
        packageSelection.classList.remove('hidden');
        serviceSelection.classList.add('hidden');
    }

    // Clear service selection and make package required
    const serviceSelect = document.getElementById('editServiceId');
    const packageSelect = document.getElementById('editPackageId');

    if (serviceSelect) serviceSelect.value = '';
    if (packageSelect) packageSelect.required = true;
    if (serviceSelect) serviceSelect.required = false;

    console.log('✅ Switched to packages tab');
}

function loadStaffForEdit(selectedStaffId) {
    fetch('<?= getBasePath() ?>/api/admin/staff.php')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('editStaffId');
            select.innerHTML = '<option value="">Select Staff</option>';

            // Handle both response formats
            const staff = data.staff || data.data || [];
            staff.forEach(staffMember => {
                const option = document.createElement('option');
                option.value = staffMember.id;
                option.textContent = staffMember.name;
                if (staffMember.id == selectedStaffId) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        });
}

function updateServiceDetails() {
    const serviceId = document.getElementById('editServiceId').value;
    console.log('Updating service details for ID:', serviceId);

    if (serviceId) {
        fetch(`<?= getBasePath() ?>/api/admin/services/get.php?id=${serviceId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Service API response:', data);
                // Handle both response formats
                const service = data.service || data;
                if (service && service.price) {
                    document.getElementById('editTotalAmount').value = service.price;
                    console.log('Updated total amount to:', service.price);
                } else {
                    console.warn('No price found in service data:', service);
                }
            })
            .catch(error => {
                console.error('Error loading service details:', error);
                showNotification('Failed to load service details', 'warning');
            });
    }
}

function updatePackageDetails() {
    const packageSelect = document.getElementById('editPackageId');
    const packageId = packageSelect ? packageSelect.value : null;

    console.log('Updating package details for ID:', packageId);

    if (packageId && packageSelect) {
        const selectedOption = packageSelect.options[packageSelect.selectedIndex];

        if (selectedOption && selectedOption.textContent) {
            // Extract price from option text: "Package Name - TSH 150"
            const priceMatch = selectedOption.textContent.match(/TSH\s+([\d,]+)/);
            if (priceMatch) {
                const price = priceMatch[1].replace(/,/g, '');
                const totalAmountField = document.getElementById('editTotalAmount');

                if (totalAmountField) {
                    totalAmountField.value = parseInt(price);
                    console.log('Updated total amount to:', price);
                }
            }
        }
    }
}

// Time slots function removed - using manual time input only

function updateBooking(event) {
    event.preventDefault();

    const bookingId = document.getElementById('editBookingId').value;

    // Get form values
    const serviceId = document.getElementById('editServiceId').value;
    const packageId = document.getElementById('editPackageId').value;
    const staffId = document.getElementById('editStaffId').value;
    const date = document.getElementById('editDate').value;
    const startTimeValue = document.getElementById('editStartTime').value;
    const status = document.getElementById('editStatus').value;
    const totalAmount = document.getElementById('editTotalAmount').value;

    console.log('Form values:', {serviceId, packageId, staffId, date, startTimeValue, status, totalAmount});

    // Validate required fields
    if (!staffId || !date || !startTimeValue || !status || !totalAmount) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    // Ensure either service or package is selected
    if (!serviceId && !packageId) {
        showNotification('Please select either a service or package', 'error');
        return;
    }

    const startTime = startTimeValue + ':00'; // Add seconds

    const formData = {
        service_id: serviceId || null,
        package_id: packageId || null,
        staff_id: staffId,
        date: date,
        start_time: startTime,
        status: status,
        total_amount: parseFloat(totalAmount),
        notes: document.getElementById('editNotes').value || ''
    };

    console.log('Sending form data:', formData);

    // Calculate end time based on service or package duration
    if (formData.start_time) {
        let duration = 60; // Default duration

        if (formData.service_id) {
            const serviceSelect = document.getElementById('editServiceId');
            const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
            const durationMatch = selectedOption.textContent.match(/(\d+)min/);
            if (durationMatch) {
                duration = parseInt(durationMatch[1]);
            }
        } else if (formData.package_id) {
            // For packages, we'll use a default duration or fetch it
            // For now, use a reasonable default
            duration = 120; // 2 hours default for packages
        }

        const startTime = new Date(`2000-01-01 ${formData.start_time}`);
        const endTime = new Date(startTime.getTime() + duration * 60000);
        formData.end_time = endTime.toTimeString().slice(0, 8); // Include seconds
    }

    fetch(`<?= getBasePath() ?>/api/admin/booking-update.php?id=${bookingId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Booking updated successfully!', 'success');
            closeEditModal();
            setTimeout(() => location.reload(), 1000); // Small delay to show notification
        } else {
            showNotification('Error updating booking: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error updating booking. Please try again.', 'error');
    });
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeStatusModal();
        closeEditModal();
    }
});

// Close modal on backdrop click
document.getElementById('statusModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeStatusModal();
    }
});

document.getElementById('editBookingModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEditModal();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
